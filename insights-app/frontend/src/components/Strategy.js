import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import axios from 'axios';
import StrategyDropdown from './StrategyDropdown';
import InsightsChart from './InsightsChart';

const Strategy = () => {
  const { strategyName } = useParams();
  const [currentStrategy, setCurrentStrategy] = useState(strategyName || 'Alle (Durchschnitt)');
  const [strategyData, setStrategyData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStrategyData(currentStrategy);
  }, [currentStrategy]);

  const fetchStrategyData = async (strategy) => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/strategy/${strategy}/`);
      setStrategyData(response.data);
    } catch (error) {
      console.error('Error fetching strategy data:', error);
    } finally {
      setLoading(false);
    }
  };






  return (
    <div>
      
      <div className="content-page">
        <div className='home-arrow-container'>
          <Link to="/" className="back-to-home-arrow">
            <i className='bi bi-arrow-left'></i> Home
          </Link>
        </div>
        <StrategyDropdown
          currentStrategy={currentStrategy}
          onStrategyChange={setCurrentStrategy}
        />
        <p className="section_title">Strategy Insights</p>
        <hr></hr>
        {loading ? (
          <div className="text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : (
          <InsightsChart
            data={strategyData}
            loading={loading}
            selectedEntity={currentStrategy}
            entityType="strategy"
            title="Strategy Insights"
          />
        )}
      </div>
    </div>
  );
};

export default Strategy;
